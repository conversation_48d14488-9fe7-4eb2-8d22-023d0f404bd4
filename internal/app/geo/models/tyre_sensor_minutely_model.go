package models

import "gopkg.in/guregu/null.v4"

type TyreSensorDataMinutely struct {
	Time                    null.Time   `json:"time" gorm:"column:time"`
	AssetID                 null.String `json:"asset_id" gorm:"column:asset_id"`
	TyreSerialNumber        null.String `json:"tyre_serial_number" gorm:"column:tyre_serial_number"`
	Ident                   null.String `json:"ident" gorm:"column:ident"`
	CreatedAt               null.Time   `json:"created_at" gorm:"column:created_at"`
	IntegrationID           null.String `json:"integration_id" gorm:"column:integration_id"`
	ClientID                null.String `json:"client_id" gorm:"column:client_id"`
	IdentMacAddress         null.String `json:"ident_mac_address" gorm:"column:ident_mac_address"`
	BatteryVoltage          null.Float  `json:"battery_voltage" gorm:"column:battery_voltage"`
	Pressure                null.Float  `json:"pressure" gorm:"column:pressure"`
	Temperature             null.Float  `json:"temperature" gorm:"column:temperature"`
	TyrePosition            null.Int    `json:"tyre_position" gorm:"column:tyre_position"`
	ParentAssetID           null.String `json:"parent_asset_id" gorm:"column:parent_asset_id"`
	TyreRowPosition         null.Int    `json:"tyre_row_position" gorm:"column:tyre_row_position"`
	LastKnownSensorTime     null.Time   `json:"last_known_sensor_time" gorm:"column:last_known_sensor_time"`
	VehicleSpeed            null.Int    `json:"vehicle_speed" gorm:"column:vehicle_speed"`
	VehicleLat              null.Float  `json:"vehicle_lat" gorm:"column:vehicle_lat"`
	VehicleLong             null.Float  `json:"vehicle_long" gorm:"column:vehicle_long"`
	PressureStatusCode      null.String `json:"pressure_status_code" gorm:"column:pressure_status_code"`
	IsHighTemperatureAlert  null.Bool   `json:"is_high_temperature_alert" gorm:"column:is_high_temperature_alert"`
	IsPressureMismatchAlert null.Bool   `json:"is_pressure_mismatch_alert" gorm:"column:is_pressure_mismatch_alert"`
	AlertID                 null.String `json:"alert_id" gorm:"column:alert_id"`
}

func (TyreSensorDataMinutely) TableName() string {
	return "tyre_sensor_data_minutely"
}
