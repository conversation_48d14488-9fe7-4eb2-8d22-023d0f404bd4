SERVER:
  DEBUG: false
  ALLOWED_HOSTS: 0.0.0.0
  HOST: 0.0.0.0
  PORT: 8000
  TIMEZONE: Asia/Jakarta
  LOG_LEVEL: info
  LOG_FILE: 

DATABASE_MASTER:
  NAME: postgres
  USER: postgres
  PORT: 5432
  LOG_MODE: false
  SSL_MODE: disable
  DEBUG: false

DATABASE_REPLICA:
  NAME: postgres
  USER: postgres
  PORT: 5432
  SSL_MODE: disable

BIGQUERY_MASTER:
  DATASET: datamart
  LOCATION: asia-southeast1

GCP_STORAGE:
  BUCKET: assetfindr_uploads

TIMESERIES_DB:
  NAME: postgres
  USER: postgres
  PORT: 5435
  LOG_MODE: true
  SSL_MODE: disable
  DEBUG: true

FLESPI:
  MQTT_TOPIC: flespi/message/gw/channels/1204835/+
  MQTT_TOPIC_BACKUP: flespi/message/gw/channels/1204836/+

SMTP:
  HOST: smtp-relay.brevo.com
  PORT: 587
  SENDER: AssetFindr <<EMAIL>>